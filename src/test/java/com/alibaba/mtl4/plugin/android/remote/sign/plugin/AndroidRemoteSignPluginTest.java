package com.alibaba.mtl4.plugin.android.remote.sign.plugin;

import com.alibaba.emas.mtl4.plugin.core.plugin.PluginContext;
import com.alibaba.emas.mtl4.plugin.core.plugin.PluginResult;
import com.alibaba.emas.mtl4.plugin.core.plugin.ShellExecutor;
import com.alibaba.emas.mtl4.plugin.core.plugin.Valve;
import com.alibaba.mtl4.plugin.android.remote.sign.plugin.config.AndroidRemoteSignPluginConfig;
import org.junit.Before;
import org.junit.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

/**
 * Android远程签名插件单元测试类
 */
public class AndroidRemoteSignPluginTest {

    @Mock
    private PluginContext<AndroidRemoteSignPluginConfig> mockContext;

    @Mock
    private AndroidRemoteSignPluginConfig mockConfig;

    @Mock
    private ShellExecutor mockShellExecutor;

    @Mock
    private Valve mockValve;

    private AndroidRemoteSignPlugin plugin;
    private Map<String, Object> configMap;
    private Map<String, String> environment;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        plugin = new AndroidRemoteSignPlugin();

        // 初始化配置映射
        configMap = new HashMap<>();
        environment = new HashMap<>();

        // 设置模拟对象行为
        when(mockContext.getConfig()).thenReturn(mockConfig);
        when(mockContext.getShellExecutor()).thenReturn(mockShellExecutor);
        when(mockContext.getValve()).thenReturn(mockValve);
        when(mockValve.getConfigMap()).thenReturn(configMap);
        when(mockContext.getEnvironment()).thenReturn(environment);

        // 设置配置对象行为
        when(mockConfig.validate()).thenReturn(true);
        when(mockConfig.getUseSigningPlatform()).thenReturn(true);
        when(mockConfig.getBuildDirs()).thenReturn("build/outputs/apk/release");
        when(mockConfig.getIsPlugin()).thenReturn(false);
        when(mockConfig.getIsRelease()).thenReturn(true);

        // 设置Shell执行器行为
        when(mockShellExecutor.execute(anyString(), any(PluginContext.class))).thenReturn("Success");
    }

    @Test
    public void testExecuteSuccess() {
        // 执行插件
        PluginResult result = plugin.execute(mockContext);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertTrue("插件应该执行成功", result.isSuccess());

        // 验证配置映射
        assertTrue("配置映射应包含needSign参数", configMap.containsKey("needSign"));
        assertEquals("needSign参数值应为true", "true", configMap.get("needSign"));

        // 验证环境变量
        assertTrue("环境变量应包含MUPP_IS_RELEASE", environment.containsKey("MUPP_IS_RELEASE"));
        assertEquals("MUPP_IS_RELEASE值应为true", "true", environment.get("MUPP_IS_RELEASE"));
    }

    @Test
    public void testExecuteWithNullConfig() {
        // 设置空配置
        when(mockContext.getConfig()).thenReturn(null);

        // 执行插件
        PluginResult result = plugin.execute(mockContext);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertFalse("插件应该执行失败", result.isSuccess());
        assertEquals("错误消息应匹配", "插件配置为空", result.getMessage());
    }

    @Test
    public void testExecuteWithInvalidConfig() {
        // 设置无效配置
        when(mockConfig.validate()).thenReturn(false);

        // 执行插件
        PluginResult result = plugin.execute(mockContext);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertFalse("插件应该执行失败", result.isSuccess());
        assertEquals("错误消息应匹配", "插件配置无效", result.getMessage());
    }

    @Test
    public void testExecuteWithSigningDisabled() {
        // 禁用签名平台
        when(mockConfig.getUseSigningPlatform()).thenReturn(false);

        // 执行插件
        PluginResult result = plugin.execute(mockContext);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertTrue("插件应该执行成功", result.isSuccess());

        // 验证配置映射不应包含签名相关参数
        assertFalse("配置映射不应包含needSign参数", configMap.containsKey("needSign"));
    }

    @Test
    public void testExecuteWithPluginMode() {
        // 启用插件模式
        when(mockConfig.getIsPlugin()).thenReturn(true);

        // 执行插件
        PluginResult result = plugin.execute(mockContext);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertTrue("插件应该执行成功", result.isSuccess());

        // 验证环境变量
        assertTrue("环境变量应包含MUPP_IS_PLUGIN", environment.containsKey("MUPP_IS_PLUGIN"));
        assertEquals("MUPP_IS_PLUGIN值应为true", "true", environment.get("MUPP_IS_PLUGIN"));
    }

    @Test
    public void testExecuteWithShellException() {
        // 设置ShellExecutor抛出异常
        when(mockShellExecutor.execute(anyString(), any(PluginContext.class)))
                .thenThrow(new RuntimeException("shell error!"));

        PluginResult result = plugin.execute(mockContext);

        assertNotNull("结果不应为空", result);
        assertFalse("插件应该执行失败", result.isSuccess());
        assertTrue("错误消息应包含异常信息", result.getMessage().contains("shell error!"));
    }
}