#!/bin/bash
# Android远程签名脚本
# 用于将未签名的APK文件通过远程签名服务进行签名
# 版本: 1.0.1
# 更新日期: $(date +"%Y-%m-%d")

# 加载用户环境变量
source ~/.bashrc || echo "" > /dev/null
source ~/.bash_profile || echo "" > /dev/null

# 设置安全选项
set -o nounset  # 使用未声明的变量时报错
set -o pipefail # 管道中的任何命令失败都会导致整个管道失败

function runShell {
    # 获取配置参数
    local MUPP_NEED_SIGN=$!valve.configMap.get("needSign")
    local MUPP_PRODUCT_NAME=$!pipeDescription.context.variableApi.get("application_identifier")
    echo "MUPP_PRODUCT_NAME:$MUPP_PRODUCT_NAME"

    # 检查是否需要签名
    if [ ! "true" = "$MUPP_NEED_SIGN" ]; then
        echo "[info] 不需要进行签名，跳过"
        return 0
    fi

    # 检查签名工具是否存在
    local remoteSignFile=~/software/remoteSign
    if [ ! -f "$remoteSignFile" ]; then
        (>&2 echo "[error] 未找到签名工具: $remoteSignFile")
        return 1
    fi

    # 检查产品名称是否存在
    if [ "" = "$MUPP_PRODUCT_NAME" ]; then
        (>&2 echo "[error] 产品名称为空")
        return 1
    fi

    # 设置产品名称和别名
    local productName=$MUPP_PRODUCT_NAME
    local productAliasName=""
    if [ ! "" = "$MUPP_KEYSTORE_NAME" ]; then
        productAliasName=$MUPP_KEYSTORE_NAME
    fi

    # 处理插件模式
    local isAddSignComment="0"
    if [ "true" = "$MUPP_IS_PLUGIN" ]; then
        isAddSignComment="1"
        productName="taobao4android"
        echo "[info] 使用插件模式签名"
    fi

    # 获取签名服务URL
    local signServerUrl=$!valve.configMap.get("signServerUrl")
    if [ "" = "$signServerUrl" ]; then
        # 如果未配置，使用默认URL
        signServerUrl="http://sign.example.com"
    fi
    echo "[info] 使用签名服务: $signServerUrl"

    # 设置构建目录
    local buildTaskId=$!pipeDescription.pipeId
    local workspace=~/.emas/build/$!{buildTaskId}/workspace
    echo "[info] 工作目录: $workspace"

    # 获取待签名文件路径
    local buildDirs=$!valve.configMap.get("buildDirs")
    echo "[info] 待签名文件路径: $buildDirs"

    # 处理多个路径
    local folders=`echo "$buildDirs" | sed "s/,/\n/g"`
    echo "$folders" | while read it ; do
        if [ -d "$workspace/$it" ]; then
            echo "[info] 处理目录: $workspace/$it"
            cd "$workspace/$it" || continue

            # 查找需要签名的文件
            local fileList=`find . -maxdepth 1 -type f \( -name "*.apk" -o -name "*.jar" \) -print`
            if [ ! "" = "$fileList" ]; then
                echo "[info] 找到待签名文件: $fileList"
                echo "$fileList" | while read line ; do
                    # 安全处理文件名
                    if [ -z "$line" ]; then
                        echo "[warning] 跳过空文件名"
                        continue
                    fi

                    # 确保文件存在且可读
                    if [ ! -f "$line" ] || [ ! -r "$line" ]; then
                        echo "[error] 文件不存在或不可读: $line"
                        continue
                    fi

                    local fileName=$(basename "$line")
                    echo "[info] 签名文件: $fileName"

                    # 验证文件扩展名
                    if [[ ! "$fileName" =~ \.(apk|jar)$ ]]; then
                        echo "[warning] 文件类型不支持，跳过: $fileName"
                        continue
                    fi

                    # 执行签名命令，使用引号保护所有参数
                    "$remoteSignFile" "$fileName" "$buildTaskId/$it" "$productName" "$productAliasName" "$isAddSignComment" "$signServerUrl" "./"

                    # 检查签名结果
                    local signResult=$?
                    if [ $signResult -eq 0 ]; then
                        echo "[info] 文件 $fileName 签名成功"
                        # 删除原始未签名文件前先确认签名后的文件存在
                        if [ -f "${fileName%.apk}-signed.apk" ] || [ -f "${fileName%.jar}-signed.jar" ]; then
                            rm -f "$fileName"
                            echo "[info] 已删除原始未签名文件: $fileName"
                        else
                            echo "[warning] 未找到签名后的文件，保留原始文件: $fileName"
                        fi
                    else
                        echo "[error] 文件 $fileName 签名失败，错误码: $signResult"
                    fi
                done
            else
                echo "[info] 目录 $workspace/$it 中未找到需要签名的文件"
            fi
        else
            echo "[warning] 目录不存在: $workspace/$it"
        fi
    done
}

function main {
    echo '[info] 开始执行远程签名脚本'
    echo "[part-separator] runShell start " `date`
    runShell
    echo "[part-separator] runShell finish " `date`
}

# catch function, do something when error occurs.
function catch {
    echo '[info] 捕获到错误，执行错误处理'
}

# finally: this function will always run
function finally {
    echo '[info] 执行最终清理工作'
}

# start run
(
    set -e
    main
)
value=$?
if [ $value != 0 ]; then
(
    set -e
    catch
)
fi
(
    set -e
    finally
)
exit $value