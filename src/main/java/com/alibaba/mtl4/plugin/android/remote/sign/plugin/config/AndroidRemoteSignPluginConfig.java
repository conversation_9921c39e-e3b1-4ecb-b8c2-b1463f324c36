package com.alibaba.mtl4.plugin.android.remote.sign.plugin.config;

import com.alibaba.emas.mtl4.plugin.core.annotations.ConfigDescription;
import com.alibaba.emas.mtl4.plugin.core.annotations.PluginConfig;
import com.alibaba.emas.mtl4.plugin.core.config.ConfigType;
import org.apache.commons.lang.StringUtils;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Android远程签名插件配置类
 * 用于配置Android应用的远程签名相关参数，包括签名平台使用、构建路径和构建类型等配置
 *
 * <AUTHOR>
 * @since 2019-09-30
 */
@PluginConfig
public class AndroidRemoteSignPluginConfig {

    @ConfigDescription(
            displayName = "使用签名平台",
            description = "是否启用远程签名平台进行APK签名",
            configType = ConfigType.RADIO,
            required = false,
            defaultValue = "true"
    )
    private Boolean useSigningPlatform = true;

    @ConfigDescription(
            displayName = "构建产物相对路径",
            description = "指定待签名APK文件的相对路径,例如: build/outputs/apk/release/app-release-unsigned.apk，多个路径使用逗号分隔",
            configType = ConfigType.TEXT,
            required = true,
            defaultValue = "build/outputs/apk/release/app-release-unsigned.apk",
            example = "build/outputs/apk/release/${applicationId}-release-unsigned.apk"
    )
    private String buildDirs = "build/outputs/apk/release/app-release-unsigned.apk";

    @ConfigDescription(
            displayName = "是否是插件",
            description = "是否是插件构建模式",
            configType = ConfigType.RADIO,
            required = false,
            defaultValue = "false"
    )
    private Boolean isPlugin = false;

    @ConfigDescription(
            displayName = "是否是release",
            description = "是否是release构建模式",
            configType = ConfigType.RADIO,
            required = false,
            defaultValue = "false"
    )
    private Boolean isRelease = false;

    @ConfigDescription(
            displayName = "签名服务URL",
            description = "远程签名服务的URL地址",
            configType = ConfigType.TEXT,
            required = false,
            defaultValue = "http://sign.example.com"
    )
    private String signServerUrl = "http://sign.example.com";

    /**
     * 默认构造函数
     */
    public AndroidRemoteSignPluginConfig() {
        // 默认构造函数
    }

    /**
     * 获取是否使用签名平台
     * @return 是否使用签名平台
     */
    public Boolean getUseSigningPlatform() {
        return useSigningPlatform;
    }

    /**
     * 设置是否使用签名平台
     * @param useSigningPlatform 是否使用签名平台
     * @return 当前配置对象，支持链式调用
     */
    public AndroidRemoteSignPluginConfig setUseSigningPlatform(Boolean useSigningPlatform) {
        this.useSigningPlatform = useSigningPlatform;
        return this;
    }

    /**
     * 获取构建产物相对路径
     * @return 构建产物相对路径
     */
    public String getBuildDirs() {
        return buildDirs;
    }

    /**
     * 获取构建产物相对路径列表
     * @return 构建产物相对路径列表
     */
    public List<String> getBuildDirsList() {
        if (StringUtils.isEmpty(buildDirs)) {
            return java.util.Collections.emptyList();
        }
        return Arrays.stream(buildDirs.split(","))
                .map(String::trim)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.toList());
    }

    /**
     * 设置构建产物相对路径
     * @param buildDirs 构建产物相对路径
     * @return 当前配置对象，支持链式调用
     */
    public AndroidRemoteSignPluginConfig setBuildDirs(String buildDirs) {
        this.buildDirs = buildDirs;
        return this;
    }

    /**
     * 获取是否是插件
     * @return 是否是插件
     */
    public Boolean getIsPlugin() {
        return isPlugin;
    }

    /**
     * 设置是否是插件
     * @param isPlugin 是否是插件
     * @return 当前配置对象，支持链式调用
     */
    public AndroidRemoteSignPluginConfig setIsPlugin(Boolean isPlugin) {
        this.isPlugin = isPlugin;
        return this;
    }

    /**
     * 获取是否是release
     * @return 是否是release
     */
    public Boolean getIsRelease() {
        return isRelease;
    }

    /**
     * 设置是否是release
     * @param isRelease 是否是release
     * @return 当前配置对象，支持链式调用
     */
    public AndroidRemoteSignPluginConfig setIsRelease(Boolean isRelease) {
        this.isRelease = isRelease;
        return this;
    }

    /**
     * 获取签名服务URL
     * @return 签名服务URL
     */
    public String getSignServerUrl() {
        return signServerUrl;
    }

    /**
     * 设置签名服务URL
     * @param signServerUrl 签名服务URL
     * @return 当前配置对象，支持链式调用
     */
    public AndroidRemoteSignPluginConfig setSignServerUrl(String signServerUrl) {
        this.signServerUrl = signServerUrl;
        return this;
    }

    /**
     * 验证配置是否有效，并返回错误信息（若有）
     * @return 错误信息，为 null 表示配置有效
     */
    public String validateWithMessage() {
        if (StringUtils.isEmpty(buildDirs)) {
            return "构建产物路径不能为空";
        }
        List<String> paths = getBuildDirsList();
        if (paths.isEmpty()) {
            return "构建产物路径格式无效";
        }
        if (Boolean.TRUE.equals(useSigningPlatform) && StringUtils.isEmpty(signServerUrl)) {
            return "签名服务URL不能为空";
        }
        if (Boolean.TRUE.equals(useSigningPlatform) && !signServerUrl.startsWith("http")) {
            return "签名服务URL格式无效，必须以http开头";
        }
        return null;
    }

    /**
     * 验证配置是否有效
     * @return 配置是否有效
     */
    public boolean validate() {
        return validateWithMessage() == null;
    }

    @Override
    public String toString() {
        return String.format(
                "AndroidRemoteSignPluginConfig{\n  useSigningPlatform=%s,\n  buildDirs='%s',\n  isPlugin=%s,\n  isRelease=%s,\n  signServerUrl='%s'\n}",
                useSigningPlatform, buildDirs, isPlugin, isRelease, signServerUrl
        );
    }
}