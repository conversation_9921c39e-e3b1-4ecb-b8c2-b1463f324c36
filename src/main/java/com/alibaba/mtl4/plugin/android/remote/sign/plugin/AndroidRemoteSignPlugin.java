package com.alibaba.mtl4.plugin.android.remote.sign.plugin;

import com.alibaba.emas.mtl4.plugin.core.annotations.Plugin;
import com.alibaba.emas.mtl4.plugin.core.annotations.PluginImplementation;
import com.alibaba.emas.mtl4.plugin.core.annotations.PluginTemplate;
import com.alibaba.emas.mtl4.plugin.core.plugin.AbstractPlugin;
import com.alibaba.emas.mtl4.plugin.core.plugin.PluginContext;
import com.alibaba.emas.mtl4.plugin.core.plugin.PluginResult;
import com.alibaba.mtl4.plugin.android.remote.sign.plugin.config.AndroidRemoteSignPluginConfig;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.File;
import java.util.HashMap;
import java.util.Map;

/**
 * Android远程签名插件实现类
 * 用于处理Android应用的远程签名，将未签名的APK文件通过远程签名服务进行签名
 *
 * <AUTHOR>
 * @since 2019-09-30
 */
@Plugin(
        id = "com.alibaba.mtl4.plugin.android-remote-sign-plugin",
        name = "Android远程签名插件",
        description = "用于Android应用的远程签名，将未签名的APK文件通过远程签名服务进行签名",
        configClass = AndroidRemoteSignPluginConfig.class
)
@PluginImplementation
public class AndroidRemoteSignPlugin extends AbstractPlugin<AndroidRemoteSignPluginConfig> {

    private static final Logger LOGGER = LoggerFactory.getLogger(AndroidRemoteSignPlugin.class);
    private static final String TEMPLATE_PATH = "android_remote_sign.vm";

    PluginTemplate(path = TEMPLATE_PATH)
    private String shellTemplate;

    /**
     * 执行插件逻辑
     * @param context 插件上下文，包含配置和执行环境
     * @return 插件执行结果
     */
    @Override
    public PluginResult execute(PluginContext<AndroidRemoteSignPluginConfig> context) {
        LOGGER.info("开始执行Android远程签名插件");

        // 获取并验证配置
        AndroidRemoteSignPluginConfig config = context.getConfig();
        if (config == null) {
            String errorMsg = "插件配置为空，无法执行签名操作";
            LOGGER.error(errorMsg);
            return PluginResult.failure(errorMsg);
        }

        // 记录配置信息
        LOGGER.info("插件配置: {}", config);

        // 验证配置有效性
        if (!config.validate()) {
            String errorMsg = "插件配置无效，请检查必填参数和格式";
            LOGGER.error("{}，配置详情: {}", errorMsg, config);
            return PluginResult.failure(errorMsg);
        }

        // 如果不使用签名平台，则跳过
        if (!config.getUseSigningPlatform()) {
            LOGGER.info("未启用远程签名平台，跳过签名步骤");
            return PluginResult.success("跳过签名步骤，未启用远程签名平台");
        }

        try {
            // 准备模板参数
            Map<String, Object> templateParams = new HashMap<>();
            templateParams.put("needSign", "true");
            templateParams.put("buildDirs", config.getBuildDirs());

            // 添加签名服务URL
            if (StringUtils.isNotEmpty(config.getSignServerUrl())) {
                templateParams.put("signServerUrl", config.getSignServerUrl());
                LOGGER.info("使用签名服务URL: {}", config.getSignServerUrl());
            }

            // 添加配置参数到模板
            context.getValve().getConfigMap().putAll(templateParams);

            // 设置环境变量
            if (config.getIsPlugin()) {
                context.getEnvironment().put("MUPP_IS_PLUGIN", "true");
            }

            if (config.getIsRelease()) {
                context.getEnvironment().put("MUPP_IS_RELEASE", "true");
            }

            // 执行shell脚本
            LOGGER.info("执行远程签名脚本");
            String result = context.getShellExecutor().execute(shellTemplate, context);
            LOGGER.info("远程签名脚本执行结果: {}", result);

            LOGGER.info("远程签名执行完成");
            return PluginResult.success("远程签名执行成功");
        } catch (Exception e) {
            String errorMsg = "执行远程签名插件时发生异常: " + e.getMessage();
            LOGGER.error(errorMsg, e);

            // 记录详细的堆栈信息，便于排查问题
            if (LOGGER.isDebugEnabled()) {
                for (StackTraceElement element : e.getStackTrace()) {
                    LOGGER.debug("  at {}", element);
                }
            }

            return PluginResult.failure(errorMsg);
        }
    }
}
